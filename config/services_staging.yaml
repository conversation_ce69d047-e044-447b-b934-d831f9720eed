services:
  _defaults:
    autowire: true
    autoconfigure: true

  App\DataTransfer\Import\SourceProcessor\EventBridgeSourceProcessor:
    arguments:
      $eventBus: '%env(EVENT_BUS_NAME)%'

  App\DataTransfer\Import\SourceProcessor\SourceProcessor:
    arguments:
      - messenger: '@Peracto\Component\DataTransfer\Import\SourceProcessor\MessageBusSourceProcessor'
        event_bridge: '@App\DataTransfer\Import\SourceProcessor\EventBridgeSourceProcessor'
      - Peracto\Component\DataTransfer\Import\Source\CsvFileSource: messenger
        Peracto\Component\DataTransfer\Import\Source\CsvFileUploadSource: messenger
      - 'messenger'

  Peracto\Component\DataTransfer\Import\SourceProcessor\SourceProcessorInterface: '@App\DataTransfer\Import\SourceProcessor\SourceProcessor'
