FEATURE_ENV=staging

###> nelmio/cors-bundle ###
CORS_ALLOW_ORIGIN=*
###< nelmio/cors-bundle ###

# The bucket where import files will be uploaded
PRIVATE_BUCKET=indespensioncouk-private-files-staging


# The database URL that will pull in values from separate env vars
DB_USER=peracto
DB_PASSWORD=password
DB_PORT=3306
DB_NAME=peracto
DB_HOST=db

# Used for the Swagger docs, which we suggest should not be present on prod
ASSETS_AWS_BASE_URL=https://example.com

ALGOLIA_APPLICATION_ID=
ALGOLIA_API_KEY=
ALGOLIA_CONTENT_INDEX_NAME=
ALGOLIA_PRODUCT_INDEX_NAME=
