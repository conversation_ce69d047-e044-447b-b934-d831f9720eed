<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250530140919 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Fix foreign key index name after table rename';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE store_product_compatibility RENAME INDEX idx_7b5992c34584665a TO IDX_7E1DE05D4584665A');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE store_product_compatibility RENAME INDEX IDX_7E1DE05D4584665A TO idx_7b5992c34584665a');
    }
}
