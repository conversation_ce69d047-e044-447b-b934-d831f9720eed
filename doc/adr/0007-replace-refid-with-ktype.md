# 6. Replace RefID with KType for Vehicle Compatibility Matching

Date: 2025-05-29

## Status

Accepted

## Context

The towbar compatibility system previously used RefID from Carweb API to determine compatible towbars for vehicles. 
However, it has been identified that the towbar data uses KType to determine compatible towbars, not RefID.

## Decision

We will replace all references to `refId` with `kType` throughout the system to accurately reflect the data being used and ensure correct towbar compatibility matching.

### Changes Include:

1. **Database Schema Changes**:
    - Rename `ref_id` column to `k_type` in `vehicle_data` table
    - Rename `ref_id` column to `k_type` in `store_products_ref_compatibility` table
    - Update associated indexes and constraints

2. **Entity and Interface Updates**:
    - Update `VehicleData` entity: `refId` property → `kType` property
    - Update `ProductCompatibility` entity: `refId` property → `kType` property
    - Update corresponding interfaces and getter/setter methods

3. **Data Import System**:
    - Update CSV import headers from `ref_id` to `k_type`
    - Update S3 vehicle data import to use KType field
    - Update data transformation and validation logic

4. **API and State Providers**:
    - Update towbar compatibility endpoint from `/towbars/compatibility/{refId}` to `/towbars/compatibility/{kType}`
    - Update Carweb integration to use KType field instead of RefID
    - Update car details API response to return `kType` instead of `refId`

5. **Repository and Service Layer**:
    - Update repository methods: `findAllForRefId()` → `findAllForKType()`
    - Update service interfaces and implementations
    - Update query builders and data access patterns

6. **Test Coverage**:
    - Update all test fixtures and expected responses
    - Update integration and unit tests
    - Update functional tests for repository methods

## Consequences

### Positive
- **Accurate Compatibility Matching**: Towbar compatibility will now correctly match against KType as used by towbar data
- **Semantic Clarity**: Code now accurately reflects the data being used (KType vs RefID)
- **API Consistency**: All endpoints consistently use KType terminology

### Negative
- **Migration Complexity**: Requires careful database migration to preserve data
- **Testing Overhead**: Extensive test updates required across the codebase
- **Documentation Updates**: All documentation must be updated to reflect new terminology
