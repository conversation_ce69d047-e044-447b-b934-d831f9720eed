<?php

declare(strict_types=1);

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity]
#[ORM\Table(name: 'store_products_ref_compatibility')]
#[ORM\UniqueConstraint(name: 'idx_k_type_product_id', columns: ['k_type', 'product_id'])]
class ProductCompatibility implements ProductCompatibilityInterface
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: Product::class)]
    #[ORM\JoinColumn(name: 'product_id', referencedColumnName: 'id', nullable: false, onDelete: 'CASCADE')]
    private ProductInterface $product;

    #[ORM\Column]
    private int $kType;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getProduct(): ProductInterface
    {
        return $this->product;
    }

    public function setProduct(ProductInterface $product): static
    {
        $this->product = $product;

        return $this;
    }

    public function getKType(): int
    {
        return $this->kType;
    }

    public function setKType(int $kType): static
    {
        $this->kType = $kType;

        return $this;
    }
}
