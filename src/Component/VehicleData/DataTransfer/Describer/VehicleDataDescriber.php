<?php

declare(strict_types=1);

namespace App\Component\VehicleData\DataTransfer\Describer;

use App\Entity\VehicleData;
use Peracto\Component\DataTransfer\Describer\DescriberInterface;
use Peracto\Component\DataTransfer\Describer\DescriberPropertyTrait;

class VehicleDataDescriber implements DescriberInterface
{
    use DescriberPropertyTrait;

    private const IMPORT_PROPERTIES = [
        ['key' => 'kType', 'required' => true],
        ['key' => 'marque', 'required' => true],
        ['key' => 'modelRange', 'required' => true],
        ['key' => 'bodyType', 'required' => true],
        ['key' => 'introYear', 'required' => true],
        ['key' => 'endYear', 'required' => false],
        ['key' => 'variant', 'required' => true],
    ];

    public function getEntityClasses(): array
    {
        return [VehicleData::class];
    }

    public function getProperties(): array
    {
        return self::IMPORT_PROPERTIES;
    }
}
