<?php

declare(strict_types=1);

namespace App\Component\VehicleData\DataTransfer\DataTransformer;

use Peracto\Component\DataTransfer\DataTransformer\DataTransformerInterface;
use Peracto\Component\DataTransfer\DataTransformer\IntPropertyConverterInterface;

readonly class VehicleDataTransformer implements DataTransformerInterface
{
    public function __construct(
        private IntPropertyConverterInterface $intPropertyConverter,
    ) {
    }

    public function transform(array $data): array
    {
        return $this->intPropertyConverter->transformData(
            $data,
            $this->getIntProperties()
        );
    }

    public function reverseTransform(array $data): array
    {
        $intProperties = $this->getIntProperties();

        if ($data['endYear'] === '') {
            $data['endYear'] = null;
            unset($intProperties['endYear']);
        }

        return $this->intPropertyConverter->reverseTransformData(
            $data,
            $intProperties
        );
    }

    protected function getIntProperties(): array
    {
        return [
            'kType',
            'introYear',
            'endYear',
        ];
    }
}
