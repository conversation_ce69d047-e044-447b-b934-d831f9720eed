<?php

declare(strict_types=1);

namespace App\Tests\Integration\Api;

use App\Entity\Product;
use App\Entity\ProductCompatibility;
use Peracto\Testing\TestCase\OptimizedJsonApiTestCase;
use Symfony\Component\HttpFoundation\Response;

class ProductCompatibilityTest extends OptimizedJsonApiTestCase
{
    public function test_deleting_product_cascades_to_compatibilities(): void
    {
        $this->authenticateClient('<EMAIL>', 'engage');

        $productRepository = $this->getEntityManager()->getRepository(Product::class);
        $productCompatibilityRepository = $this->getEntityManager()->getRepository(ProductCompatibility::class);

        $product = $productRepository->findOneBy(['sku' => 'PK123456']);
        self::assertNotNull($product);

        $compatibilities = $productCompatibilityRepository
            ->findBy(['product' => $product]);

        self::assertNotEmpty($compatibilities);

        $productId = $product->getId();
        $compatibilityIds = array_map(static fn($compatibility) => $compatibility->getId(), $compatibilities);

        $response = $this->client->request('DELETE', '/products/' . $productId);
        $this->assertResponseCode($response, Response::HTTP_NO_CONTENT);

        $this->getEntityManager()->clear();

        $deletedProduct = $productRepository->find($productId);
        self::assertNull($deletedProduct);

        foreach ($compatibilityIds as $compatibilityId) {
            $deletedComp = $this->getEntityManager()->find(ProductCompatibility::class, $compatibilityId);
            self::assertNull($deletedComp, sprintf('Compatibility %d should be deleted', $compatibilityId));
        }
    }
}
